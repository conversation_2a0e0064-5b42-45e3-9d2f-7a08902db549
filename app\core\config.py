"""
Application configuration settings
"""

from pydantic_settings import BaseSettings
from typing import List, Optional
import os


class Settings(BaseSettings):
    """Application settings with environment variable support."""
    
    # Application settings
    APP_NAME: str = "VPSScriptHelper-BlueBlue"
    VERSION: str = "1.0.0"
    DEBUG: bool = False
    HOST: str = "0.0.0.0"
    PORT: int = 8000
    
    # Security settings
    SECRET_KEY: str = "your-secret-key-change-this-in-production"
    ALGORITHM: str = "HS256"
    ACCESS_TOKEN_EXPIRE_MINUTES: int = 30
    
    # Database settings
    DATABASE_URL: str = "sqlite+aiosqlite:///./blueblue.db"
    
    # CORS settings
    ALLOWED_HOSTS: str = "*"

    @property
    def allowed_hosts_list(self) -> List[str]:
        """Convert ALLOWED_HOSTS string to list."""
        if self.ALLOWED_HOSTS == "*":
            return ["*"]
        return [host.strip() for host in self.ALLOWED_HOSTS.split(",") if host.strip()]
    
    # SSH settings
    SSH_TIMEOUT: int = 30
    SSH_RETRY_ATTEMPTS: int = 3
    SSH_RETRY_DELAY: int = 5
    
    # Xray configuration
    XRAY_CONFIG_PATH: str = "/etc/xray/config.json"
    XRAY_SERVICE_NAME: str = "xray"
    XRAY_BACKUP_PATH: str = "/etc/xray/config.json.backup"
    
    # Task scheduling
    EXPIRY_CHECK_INTERVAL_HOURS: int = 24
    AUTO_REMOVE_EXPIRED: bool = True
    
    # Logging
    LOG_LEVEL: str = "INFO"
    LOG_FILE: Optional[str] = None
    
    # File paths
    UPLOAD_DIR: str = "./uploads"
    BACKUP_DIR: str = "./backups"
    
    class Config:
        env_file = ".env"
        case_sensitive = True
        extra = "ignore"


# Global settings instance
settings = Settings()


# Environment-specific configurations
class DevelopmentSettings(Settings):
    """Development environment settings."""
    DEBUG: bool = True
    LOG_LEVEL: str = "DEBUG"


class ProductionSettings(Settings):
    """Production environment settings."""
    DEBUG: bool = False
    LOG_LEVEL: str = "WARNING"
    ALLOWED_HOSTS: str = "localhost,127.0.0.1"


class TestingSettings(Settings):
    """Testing environment settings."""
    DATABASE_URL: str = "sqlite+aiosqlite:///./test.db"
    SECRET_KEY: str = "test-secret-key"
    ACCESS_TOKEN_EXPIRE_MINUTES: int = 5


def get_settings() -> Settings:
    """Get settings based on environment."""
    env = os.getenv("ENVIRONMENT", "development").lower()
    
    if env == "production":
        return ProductionSettings()
    elif env == "testing":
        return TestingSettings()
    else:
        return DevelopmentSettings()


# Use environment-specific settings
settings = get_settings()
